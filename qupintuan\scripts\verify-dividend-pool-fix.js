#!/usr/bin/env node

/**
 * NodeStaking分红池修复验证脚本
 * 用于验证前端修复是否正确显示分红池数据
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 验证NodeStaking分红池修复...\n');

// 检查前端文件是否正确修复
function verifyFrontendFix() {
  const filePath = path.join(__dirname, '../src/components/Finance/NodeStaking/NodeStakingSimplified.jsx');
  
  if (!fs.existsSync(filePath)) {
    console.log('❌ 前端文件不存在:', filePath);
    return false;
  }

  const content = fs.readFileSync(filePath, 'utf8');
  
  // 检查关键修复点
  const checks = [
    {
      name: '获取availableDividendPool字段',
      pattern: /availableDividendPool:\s*formatUnits\(dividendDetails\.availableDividendPool/,
      required: true
    },
    {
      name: '获取todayNewDividends字段',
      pattern: /todayNewDividends:\s*formatUnits\(dividendDetails\.todayNewDividends/,
      required: true
    },
    {
      name: '显示可分配分红池',
      pattern: /可分配分红池.*stakingData\.availableDividendPool/,
      required: true
    },
    {
      name: '显示今日新增分红',
      pattern: /今日新增分红.*stakingData\.todayNewDividends/,
      required: true
    }
  ];

  let allPassed = true;
  
  console.log('📋 前端修复检查结果:');
  console.log('===================');
  
  checks.forEach(check => {
    const passed = check.pattern.test(content);
    const status = passed ? '✅' : '❌';
    console.log(`${status} ${check.name}: ${passed ? '通过' : '失败'}`);
    
    if (check.required && !passed) {
      allPassed = false;
    }
  });

  return allPassed;
}

// 检查合约ABI是否包含必要的函数
function verifyContractABI() {
  console.log('\n📋 合约ABI检查:');
  console.log('===============');
  
  try {
    const contractsPath = path.join(__dirname, '../src/contracts');
    const files = fs.readdirSync(contractsPath);
    
    // 查找包含NodeStaking ABI的文件
    let abiFound = false;
    let hasDividendDetails = false;
    
    files.forEach(file => {
      if (file.endsWith('.js') || file.endsWith('.json')) {
        const filePath = path.join(contractsPath, file);
        const content = fs.readFileSync(filePath, 'utf8');
        
        if (content.includes('NodeStaking') && content.includes('getDividendDetails')) {
          abiFound = true;
          hasDividendDetails = true;
        }
      }
    });

    console.log(`✅ NodeStaking ABI: ${abiFound ? '找到' : '未找到'}`);
    console.log(`✅ getDividendDetails函数: ${hasDividendDetails ? '存在' : '不存在'}`);
    
    return abiFound && hasDividendDetails;
    
  } catch (error) {
    console.log('❌ ABI检查失败:', error.message);
    return false;
  }
}

// 生成测试建议
function generateTestSuggestions() {
  console.log('\n🧪 测试建议:');
  console.log('============');
  console.log('1. 连接钱包到NodeStaking页面');
  console.log('2. 检查是否显示以下字段:');
  console.log('   - 合约总余额');
  console.log('   - 可分配分红池');
  console.log('   - 今日新增分红');
  console.log('   - 每节点可领取');
  console.log('3. 如果可分配分红池为0但合约有余额:');
  console.log('   - 点击"计算今日分红"按钮');
  console.log('   - 等待交易确认');
  console.log('   - 刷新页面查看变化');
  console.log('4. 验证数据一致性:');
  console.log('   - 可分配分红池 + 今日新增分红 = 合约总余额');
  console.log('   - 每节点可领取 = 可分配分红池 ÷ 有效节点数');
}

// 生成问题排查指南
function generateTroubleshootingGuide() {
  console.log('\n🔧 问题排查指南:');
  console.log('================');
  console.log('如果分红池仍然显示为0:');
  console.log('1. 检查网络连接和RPC状态');
  console.log('2. 确认合约地址正确');
  console.log('3. 验证当前时间是否已过00:00 UTC');
  console.log('4. 尝试手动调用calculateDailyReward()');
  console.log('5. 检查浏览器控制台是否有错误信息');
  console.log('6. 联系技术支持并提供以下信息:');
  console.log('   - 钱包地址');
  console.log('   - 操作时间');
  console.log('   - 错误截图');
}

// 主函数
function main() {
  let success = true;
  
  // 验证前端修复
  if (!verifyFrontendFix()) {
    success = false;
  }
  
  // 验证合约ABI
  if (!verifyContractABI()) {
    success = false;
  }
  
  console.log('\n📊 总体结果:');
  console.log('============');
  
  if (success) {
    console.log('✅ 所有检查通过，修复应该生效');
    console.log('💡 建议进行实际测试验证');
  } else {
    console.log('❌ 发现问题，需要进一步修复');
  }
  
  generateTestSuggestions();
  generateTroubleshootingGuide();
  
  console.log('\n📝 修复总结:');
  console.log('============');
  console.log('1. ✅ 前端代码已修复，正确获取分红池数据');
  console.log('2. ✅ 添加了今日新增分红显示');
  console.log('3. ✅ 完善了分红池状态可视化');
  console.log('4. 💡 用户需要手动触发分红池转换');
  console.log('5. 💡 建议添加自动化触发机制（长期优化）');
  
  return success;
}

// 运行验证
if (require.main === module) {
  const result = main();
  process.exit(result ? 0 : 1);
}

module.exports = { verifyFrontendFix, verifyContractABI };
