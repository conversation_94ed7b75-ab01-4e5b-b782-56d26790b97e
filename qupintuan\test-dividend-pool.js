// 测试NodeStaking合约分红池逻辑
const { ethers } = require('ethers');

// 合约地址和ABI
const CONTRACT_ADDRESSES = {
  NodeStaking: '******************************************', // 替换为实际地址
  USDT: '******************************************' // 替换为实际地址
};

// 简化的ABI（只包含需要的函数）
const NODE_STAKING_ABI = [
  'function getDividendDetails() view returns (tuple(uint256 totalDividends, uint256 todayNewDividends, uint256 availableDividendPool, uint256 totalEffectiveNodes, uint256 rewardPerNode, uint256 lastCalculateTime, bool canCalculateToday))',
  'function getAvailableDividendPool() view returns (uint256)',
  'function getTodayNewDividends() view returns (uint256)',
  'function calculateDailyReward()',
  'function receiveDividendFund(uint256 amount)',
  'function currentDay() view returns (uint256)',
  'function lastPoolUpdateDay() view returns (uint256)',
  'function totalEffectiveNodes() view returns (uint256)',
  'function dailyRewardPerNode() view returns (uint256)'
];

const USDT_ABI = [
  'function balanceOf(address account) view returns (uint256)',
  'function transfer(address to, uint256 amount) returns (bool)',
  'function approve(address spender, uint256 amount) returns (bool)'
];

async function testDividendPool() {
  console.log('🔍 测试NodeStaking合约分红池逻辑...\n');

  // 连接到BSC测试网
  const provider = new ethers.JsonRpcProvider('https://data-seed-prebsc-1-s1.binance.org:8545/');
  
  // 创建合约实例
  const nodeStaking = new ethers.Contract(CONTRACT_ADDRESSES.NodeStaking, NODE_STAKING_ABI, provider);
  const usdt = new ethers.Contract(CONTRACT_ADDRESSES.USDT, USDT_ABI, provider);

  try {
    console.log('📊 当前合约状态:');
    console.log('================');

    // 1. 获取合约USDT余额
    const contractBalance = await usdt.balanceOf(CONTRACT_ADDRESSES.NodeStaking);
    console.log(`合约USDT余额: ${ethers.formatUnits(contractBalance, 6)} USDT`);

    // 2. 获取分红池详细信息
    const dividendDetails = await nodeStaking.getDividendDetails();
    console.log(`总分红: ${ethers.formatUnits(dividendDetails.totalDividends, 6)} USDT`);
    console.log(`可分配分红池: ${ethers.formatUnits(dividendDetails.availableDividendPool, 6)} USDT`);
    console.log(`今日新增分红: ${ethers.formatUnits(dividendDetails.todayNewDividends, 6)} USDT`);
    console.log(`每节点可领取: ${ethers.formatUnits(dividendDetails.rewardPerNode, 6)} USDT`);
    console.log(`有效节点数: ${dividendDetails.totalEffectiveNodes.toString()}`);
    console.log(`今日可计算分红: ${dividendDetails.canCalculateToday ? '是' : '否'}`);

    // 3. 获取时间相关信息
    const currentDay = await nodeStaking.currentDay();
    const lastPoolUpdateDay = await nodeStaking.lastPoolUpdateDay();
    const currentTimestamp = Math.floor(Date.now() / 1000);
    const currentDayFromTimestamp = Math.floor(currentTimestamp / (24 * 60 * 60));

    console.log('\n⏰ 时间信息:');
    console.log('============');
    console.log(`当前时间戳: ${currentTimestamp}`);
    console.log(`当前日期(天数): ${currentDayFromTimestamp}`);
    console.log(`合约当前日期: ${currentDay.toString()}`);
    console.log(`上次分红池更新日期: ${lastPoolUpdateDay.toString()}`);

    // 4. 分析分红池状态
    console.log('\n🔍 分红池状态分析:');
    console.log('==================');
    
    const totalBalance = parseFloat(ethers.formatUnits(dividendDetails.totalDividends, 6));
    const availablePool = parseFloat(ethers.formatUnits(dividendDetails.availableDividendPool, 6));
    const todayNew = parseFloat(ethers.formatUnits(dividendDetails.todayNewDividends, 6));

    if (totalBalance === 0) {
      console.log('❌ 合约中没有USDT余额');
    } else if (availablePool === 0) {
      console.log('⚠️  可分配分红池为0，但合约有余额');
      console.log('   这表明资金还未转入可分配池');
      
      if (currentDayFromTimestamp > lastPoolUpdateDay) {
        console.log('   ✅ 当前时间已过00:00，应该可以转换');
        console.log('   💡 需要调用calculateDailyReward()或receiveDividendFund()来触发转换');
      } else {
        console.log('   ⏳ 还未到次日00:00，需要等待');
      }
    } else {
      console.log('✅ 可分配分红池有余额，状态正常');
    }

    // 5. 检查是否需要手动触发更新
    console.log('\n🛠️  修复建议:');
    console.log('=============');
    
    if (totalBalance > 0 && availablePool === 0 && currentDayFromTimestamp > lastPoolUpdateDay) {
      console.log('1. 问题确认: 分红池转换逻辑需要手动触发');
      console.log('2. 解决方案: 调用calculateDailyReward()函数来触发分红池更新');
      console.log('3. 技术原因: _updateDividendPool()只在特定函数调用时执行');
      console.log('4. 长期方案: 考虑添加定时任务或自动触发机制');
    } else if (totalBalance === 0) {
      console.log('1. 需要向合约转入USDT资金');
      console.log('2. 使用receiveDividendFund()函数转入资金');
    } else {
      console.log('✅ 分红池状态正常，无需修复');
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    
    if (error.message.includes('call revert exception')) {
      console.log('\n💡 可能的原因:');
      console.log('- 合约地址不正确');
      console.log('- 网络连接问题');
      console.log('- 合约函数签名不匹配');
    }
  }
}

// 运行测试
if (require.main === module) {
  testDividendPool().catch(console.error);
}

module.exports = { testDividendPool };
