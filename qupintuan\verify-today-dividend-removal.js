#!/usr/bin/env node

/**
 * 验证今日新增分红显示已正确移除
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 验证今日新增分红显示移除...\n');

function verifyRemoval() {
  const filePath = path.join(__dirname, 'src/components/Finance/NodeStaking/NodeStakingSimplified.jsx');
  
  if (!fs.existsSync(filePath)) {
    console.log('❌ 文件不存在:', filePath);
    return false;
  }

  const content = fs.readFileSync(filePath, 'utf8');
  
  // 检查是否已移除相关代码
  const checks = [
    {
      name: '移除todayNewDividends字段获取',
      pattern: /todayNewDividends:\s*formatUnits/,
      shouldNotExist: true
    },
    {
      name: '移除今日新增分红UI显示',
      pattern: /今日新增分红.*stakingData\.todayNewDividends/,
      shouldNotExist: true
    },
    {
      name: '保留可分配分红池显示',
      pattern: /可分配分红池.*stakingData\.availableDividendPool/,
      shouldExist: true
    },
    {
      name: '保留availableDividendPool字段获取',
      pattern: /availableDividendPool:\s*formatUnits\(dividendDetails\.availableDividendPool/,
      shouldExist: true
    }
  ];

  let allPassed = true;
  
  console.log('📋 验证结果:');
  console.log('============');
  
  checks.forEach(check => {
    const found = check.pattern.test(content);
    let passed;
    
    if (check.shouldNotExist) {
      passed = !found;
      const status = passed ? '✅' : '❌';
      console.log(`${status} ${check.name}: ${passed ? '已移除' : '仍然存在'}`);
    } else if (check.shouldExist) {
      passed = found;
      const status = passed ? '✅' : '❌';
      console.log(`${status} ${check.name}: ${passed ? '存在' : '缺失'}`);
    }
    
    if (!passed) {
      allPassed = false;
    }
  });

  return allPassed;
}

function generateSummary() {
  console.log('\n📝 修复总结:');
  console.log('============');
  console.log('✅ 问题识别: 合约中todayNewDividends计算逻辑不准确');
  console.log('✅ 解决方案: 移除前端的今日新增分红显示');
  console.log('✅ 保留功能: 可分配分红池显示正常');
  console.log('✅ 用户体验: 避免显示错误数据造成困扰');
  
  console.log('\n🎯 当前显示内容:');
  console.log('================');
  console.log('- 合约总余额: 显示合约中的总USDT余额');
  console.log('- 可分配分红池: 显示可用于分红的USDT金额');
  console.log('- 每节点可领取: 显示每个节点可领取的分红金额');
  console.log('- 有效节点数: 显示当前活跃的节点数量');
  console.log('- 剩余节点: 显示还可以激活的节点数量');
  
  console.log('\n💡 用户操作指南:');
  console.log('================');
  console.log('1. 查看"可分配分红池"余额');
  console.log('2. 如果为0但"合约总余额"不为0，点击"计算今日分红"');
  console.log('3. 等待交易确认后，分红池余额会更新');
  console.log('4. 分红池有余额后，即可领取分红');
}

// 主函数
function main() {
  const success = verifyRemoval();
  
  console.log('\n📊 验证结果:');
  console.log('============');
  
  if (success) {
    console.log('✅ 今日新增分红显示已正确移除');
    console.log('✅ 可分配分红池显示保持正常');
    console.log('✅ 修复完成，避免了用户困扰');
  } else {
    console.log('❌ 发现问题，需要进一步检查');
  }
  
  generateSummary();
  
  return success;
}

// 运行验证
if (require.main === module) {
  const result = main();
  process.exit(result ? 0 : 1);
}

module.exports = { verifyRemoval };
